# Makefile for Telegram MCP Server

.PHONY: help install setup test run clean dev lint format

# Default target
help:
	@echo "Telegram MCP Server - Available Commands:"
	@echo ""
	@echo "  setup     - Run initial setup and configuration"
	@echo "  install   - Install dependencies"
	@echo "  test      - Run installation tests"
	@echo "  run       - Start the server"
	@echo "  dev       - Run in development mode with debug logging"
	@echo "  mcp-only  - Run only the MCP server"
	@echo "  bot-only  - Run only the Telegram bot"
	@echo "  clean     - Clean up generated files"
	@echo "  lint      - Run code linting"
	@echo "  format    - Format code"
	@echo "  logs      - Show recent logs"
	@echo ""

# Install dependencies
install:
	@echo "Installing dependencies..."
	pip install -r requirements.txt

# Run setup
setup:
	@echo "Running setup..."
	python setup.py

# Run tests
test:
	@echo "Running tests..."
	python test_installation.py

# Start the server
run:
	@echo "Starting Telegram MCP Server..."
	python main.py

# Development mode
dev:
	@echo "Starting in development mode..."
	python main.py --debug

# Run only MCP server
mcp-only:
	@echo "Starting MCP server only..."
	python mcp_server.py

# Run only Telegram bot
bot-only:
	@echo "Starting Telegram bot only..."
	python telegram_bot.py

# Clean up
clean:
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -f data/test.db
	rm -f *.log

# Lint code
lint:
	@echo "Running linting..."
	python -m flake8 *.py --max-line-length=100 --ignore=E203,W503

# Format code
format:
	@echo "Formatting code..."
	python -m black *.py --line-length=100
	python -m isort *.py

# Show logs
logs:
	@echo "Recent logs:"
	@if [ -f data/app.log ]; then tail -n 50 data/app.log; else echo "No log file found"; fi

# Check status
status:
	@echo "Checking system status..."
	@if pgrep -f "python main.py" > /dev/null; then \
		echo "✅ Server is running"; \
	else \
		echo "❌ Server is not running"; \
	fi
	@if [ -f config/config.json ]; then \
		echo "✅ Configuration file exists"; \
	else \
		echo "❌ Configuration file missing"; \
	fi

# Quick start (for new installations)
quickstart: install setup test
	@echo ""
	@echo "🎉 Quick start completed!"
	@echo "Next steps:"
	@echo "1. Configure Claude Desktop (see USAGE_GUIDE.md)"
	@echo "2. Run: make run"

# Development setup
dev-setup: install
	@echo "Installing development dependencies..."
	pip install black isort flake8 pytest pytest-asyncio
	@echo "Development setup complete"

# Create backup
backup:
	@echo "Creating backup..."
	@mkdir -p backups
	@tar -czf backups/backup-$(shell date +%Y%m%d-%H%M%S).tar.gz \
		config/ data/ *.py requirements.txt README.md
	@echo "Backup created in backups/"

# Restore from backup
restore:
	@echo "Available backups:"
	@ls -la backups/ 2>/dev/null || echo "No backups found"
	@echo "To restore: tar -xzf backups/backup-YYYYMMDD-HHMMSS.tar.gz"

# Update dependencies
update:
	@echo "Updating dependencies..."
	pip install --upgrade -r requirements.txt

# Check configuration
check-config:
	@echo "Checking configuration..."
	@python -c "import json; json.load(open('config/config.json')); print('✅ Configuration is valid')" 2>/dev/null || echo "❌ Configuration is invalid"

# Monitor logs in real-time
monitor:
	@echo "Monitoring logs (Ctrl+C to stop)..."
	@tail -f data/app.log 2>/dev/null || echo "No log file found. Start the server first."

# Generate documentation
docs:
	@echo "Documentation files:"
	@echo "  README.md - Main documentation"
	@echo "  USAGE_GUIDE.md - Detailed usage guide"
	@echo "  config/config.example.json - Configuration template"

# Docker support (if needed)
docker-build:
	@echo "Building Docker image..."
	docker build -t telegram-mcp-server .

docker-run:
	@echo "Running Docker container..."
	docker run -d --name telegram-mcp-server \
		-v $(PWD)/config:/app/config \
		-v $(PWD)/data:/app/data \
		telegram-mcp-server

# System requirements check
check-system:
	@echo "Checking system requirements..."
	@python --version | grep -E "3\.(10|11|12)" > /dev/null && echo "✅ Python version OK" || echo "❌ Python 3.10+ required"
	@which pip > /dev/null && echo "✅ pip available" || echo "❌ pip not found"
	@which git > /dev/null && echo "✅ git available" || echo "❌ git not found"
