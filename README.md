# Telegram MCP Server

A Model Context Protocol (MCP) server that enables bidirectional communication between AI agents and Telegram users through a Telegram bot.

## 🌟 Features

- 🤖 **Telegram Bot Integration**: Send and receive messages through Telegram
- 🔄 **Bidirectional Communication**: AI agents can send notifications to users
- 📱 **Command Interface**: Execute AI agent commands via Telegram
- 💾 **Message Persistence**: Store conversation history and context
- 🔐 **User Authentication**: Secure access control for authorized users
- 📊 **Session Management**: Track user sessions and context
- ⚡ **Async Processing**: High-performance asynchronous message handling
- 🛡️ **Rate Limiting**: Built-in protection against spam and abuse
- 📈 **Monitoring**: Comprehensive logging and status monitoring

## 🏗️ Architecture

The system consists of several components working together:

1. **MCP Server**: Implements the Model Context Protocol for AI agent communication
2. **Telegram Bot**: Handles Telegram API integration and user interactions
3. **Message Bridge**: Coordinates communication between MCP and Telegram
4. **Storage Layer**: Manages data persistence and configuration
5. **Queue System**: Handles message routing and retry logic

## Installation

### Prerequisites

- Python 3.10 or higher
- Teleg<PERSON> <PERSON><PERSON> (from @<PERSON>t<PERSON>ather)
- AI Agent with MCP support (e.g., <PERSON>)

### Setup

1. <PERSON>lone the repository:
```bash
git clone <repository-url>
cd telegram-mcp-server
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure the system:
```bash
cp config/config.example.json config/config.json
# Edit config.json with your settings
```

4. Set up your Telegram bot:
   - Message @BotFather on Telegram
   - Create a new bot with `/newbot`
   - Save the bot token to your config

5. Run the server:
```bash
python main.py
```

## Configuration

### MCP Client Setup

Add the following to your Claude Desktop configuration (`claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "telegram-notifications": {
      "command": "python",
      "args": ["/absolute/path/to/telegram-mcp-server/mcp_server.py"]
    }
  }
}
```

### Bot Configuration

Edit `config/config.json`:

```json
{
  "telegram": {
    "bot_token": "YOUR_BOT_TOKEN",
    "allowed_users": [123456789],
    "admin_users": [123456789]
  },
  "mcp": {
    "server_name": "telegram-notifications",
    "version": "1.0.0"
  },
  "database": {
    "path": "data/messages.db"
  }
}
```

## Usage

### Basic Commands

- `/start` - Initialize the bot
- `/help` - Show available commands
- `/status` - Check connection status
- `/history` - View recent messages

### AI Agent Integration

The MCP server provides these tools to AI agents:

1. **send_telegram_message**: Send messages to Telegram users
2. **get_telegram_messages**: Retrieve recent messages
3. **list_telegram_users**: Get list of active users
4. **set_notification_preferences**: Configure user notifications

### Example Usage

1. **Send a notification from AI agent**:
   ```
   AI Agent: "Send a notification to user 123456789 saying 'Task completed successfully'"
   ```

2. **Receive commands from Telegram**:
   ```
   Telegram User: "/ask What's the weather like today?"
   AI Agent: Processes the request and responds via MCP
   ```

## Development

### Project Structure

```
telegram-mcp-server/
├── main.py                 # Application entry point
├── mcp_server.py           # MCP server implementation
├── telegram_bot.py         # Telegram bot implementation
├── message_bridge.py       # Communication bridge
├── database.py             # Database operations
├── config/
│   ├── config.json         # Configuration file
│   └── config.example.json # Configuration template
├── data/                   # Database and logs
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

### Running in Development

```bash
# Run with debug logging
python main.py --debug

# Run only the MCP server
python mcp_server.py

# Run only the Telegram bot
python telegram_bot.py
```

## Limitations and Considerations

1. **Security**: Ensure proper user authentication and rate limiting
2. **Scalability**: Current implementation uses SQLite (suitable for small-scale use)
3. **Error Handling**: Implement robust error handling for network issues
4. **Message Formatting**: Handle rich text and media appropriately
5. **Rate Limits**: Respect Telegram API rate limits

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
