# Telegram MCP Server Usage Guide

This guide provides detailed instructions on how to use the Telegram MCP Server for bidirectional communication between AI agents and Telegram users.

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd telegram-mcp-server

# Run setup
python setup.py

# Test installation
python test_installation.py

# Start the server
python main.py
```

### 2. Configure <PERSON>

Add this to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "telegram-notifications": {
      "command": "python",
      "args": ["/absolute/path/to/telegram-mcp-server/mcp_server.py"]
    }
  }
}
```

## Telegram Bot Commands

### User Commands

- **`/start`** - Initialize the bot and show welcome message
- **`/help`** - Display available commands and usage instructions
- **`/status`** - Check system status and connection health
- **`/history [limit]`** - View recent message history (default: 5 messages)
- **`/ask <question>`** - Ask a question to the AI agent

### Admin Commands

- **`/notify <user_id> <message>`** - Send notification to specific user

### Message Handling

Any text message sent to the bot (not starting with `/`) will be forwarded to the AI agent for processing.

## MCP Tools for AI Agents

The MCP server provides these tools that AI agents can use:

### 1. `send_telegram_message`

Send a message to a Telegram user.

**Parameters:**
- `user_id` (int): Telegram user ID
- `message` (str): Message content
- `message_type` (str, optional): Type of message (notification, alert, response, etc.)

**Example:**
```
AI Agent: Use send_telegram_message with user_id=123456789, message="Task completed successfully", message_type="notification"
```

### 2. `get_telegram_messages`

Retrieve recent Telegram messages.

**Parameters:**
- `user_id` (int, optional): Filter by user ID
- `limit` (int, optional): Maximum messages to retrieve (default: 10)
- `message_type` (str, optional): Filter by message type
- `unprocessed_only` (bool, optional): Only unprocessed messages

**Example:**
```
AI Agent: Use get_telegram_messages with limit=5, unprocessed_only=true
```

### 3. `list_telegram_users`

Get list of active Telegram users.

**Returns:** JSON array of active users with session information.

### 4. `set_notification_preferences`

Configure notification preferences for a user.

**Parameters:**
- `user_id` (int): Telegram user ID
- `preferences` (dict): Notification preferences

### 5. `get_server_status`

Get current server status and health information.

### 6. `mark_messages_processed`

Mark multiple messages as processed.

**Parameters:**
- `message_ids` (list): List of message IDs to mark as processed

## Usage Scenarios

### Scenario 1: AI Agent Sends Notification

1. AI agent detects an important event
2. AI agent calls `send_telegram_message`:
   ```
   user_id: 123456789
   message: "🚨 Alert: Server CPU usage is at 95%"
   message_type: "alert"
   ```
3. User receives notification on Telegram
4. User can respond via Telegram

### Scenario 2: User Asks Question

1. User sends message to Telegram bot: `/ask What's the weather like?`
2. Bot forwards question to AI agent via MCP
3. AI agent processes question and responds
4. AI agent calls `send_telegram_message` with the answer
5. User receives response on Telegram

### Scenario 3: Monitoring and Alerts

1. AI agent monitors system metrics
2. When threshold exceeded, AI agent sends alert:
   ```
   send_telegram_message(
     user_id=admin_user_id,
     message="Database backup completed successfully",
     message_type="system"
   )
   ```

### Scenario 4: Interactive Conversation

1. User: "Can you help me with my project?"
2. AI Agent: "Of course! What kind of project are you working on?"
3. User: "I'm building a web application"
4. AI Agent: "Great! What technology stack are you using?"
5. (Conversation continues...)

## Configuration Options

### Telegram Settings

```json
{
  "telegram": {
    "bot_token": "your_bot_token",
    "allowed_users": [123456789, 987654321],
    "admin_users": [123456789],
    "polling_interval": 1.0,
    "max_message_length": 4096
  }
}
```

### Security Settings

```json
{
  "security": {
    "rate_limit_messages_per_minute": 30,
    "rate_limit_commands_per_minute": 10,
    "session_timeout_minutes": 60,
    "max_concurrent_sessions": 100
  }
}
```

### Notification Settings

```json
{
  "notifications": {
    "default_enabled": true,
    "types": {
      "system": true,
      "alerts": true,
      "updates": true,
      "responses": true
    },
    "quiet_hours": {
      "enabled": false,
      "start": "22:00",
      "end": "08:00",
      "timezone": "UTC"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check bot token in configuration
   - Verify user ID is in allowed_users list
   - Check server logs for errors

2. **MCP tools not available in Claude Desktop**
   - Verify claude_desktop_config.json path is correct
   - Restart Claude Desktop after configuration changes
   - Check MCP server is running: `python mcp_server.py`

3. **Database errors**
   - Ensure data directory exists and is writable
   - Check database path in configuration
   - Run: `python test_installation.py`

4. **Rate limiting**
   - Adjust rate limits in configuration
   - Wait for rate limit window to reset
   - Check user message frequency

### Debug Mode

Run with debug logging:

```bash
python main.py --debug
```

### Log Files

Check these log files for troubleshooting:
- `data/app.log` - Application logs
- `data/telegram.log` - Telegram bot logs
- `data/mcp.log` - MCP server logs

## Advanced Usage

### Custom Message Types

You can define custom message types for different purposes:

```python
# In your AI agent
send_telegram_message(
    user_id=user_id,
    message="Deployment completed",
    message_type="deployment"
)
```

### Broadcast Messages

Send messages to all active users:

```python
# This would be implemented as an additional MCP tool
broadcast_message(
    message="System maintenance in 30 minutes",
    message_type="announcement"
)
```

### Rich Formatting

Use Markdown formatting in messages:

```python
send_telegram_message(
    user_id=user_id,
    message="**Important Update**\n\n• Feature A deployed\n• Bug fixes applied\n• Performance improved",
    message_type="update"
)
```

## Security Considerations

1. **User Authentication**: Only authorized users can interact with the bot
2. **Rate Limiting**: Prevents spam and abuse
3. **Message Validation**: All messages are validated before processing
4. **Secure Storage**: Sensitive data is properly encrypted
5. **Access Control**: Admin functions require admin privileges

## Performance Tips

1. **Database Cleanup**: Regularly clean old messages
2. **Rate Limiting**: Adjust limits based on usage patterns
3. **Message Queuing**: System handles high message volumes gracefully
4. **Resource Monitoring**: Monitor CPU and memory usage

## Integration Examples

### With Monitoring Systems

```python
# Prometheus alert integration
if cpu_usage > 90:
    send_telegram_message(
        user_id=admin_id,
        message=f"🚨 High CPU usage: {cpu_usage}%",
        message_type="alert"
    )
```

### With CI/CD Pipelines

```python
# GitHub Actions integration
send_telegram_message(
    user_id=dev_team_id,
    message="✅ Build #123 deployed to production",
    message_type="deployment"
)
```

### With Business Logic

```python
# Order processing
send_telegram_message(
    user_id=customer_id,
    message="📦 Your order #12345 has been shipped!",
    message_type="notification"
)
```
