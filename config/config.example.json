{"telegram": {"bot_token": "YOUR_BOT_TOKEN_HERE", "allowed_users": [123456789], "admin_users": [123456789], "webhook_url": null, "webhook_port": 8443, "polling_interval": 1.0, "max_message_length": 4096}, "mcp": {"server_name": "telegram-notifications", "version": "1.0.0", "description": "MCP server for Telegram bot integration", "transport": "stdio", "capabilities": {"tools": true, "resources": true, "prompts": false, "sampling": false}}, "database": {"path": "data/messages.db", "max_message_history": 1000, "cleanup_interval_hours": 24}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "data/app.log", "max_file_size_mb": 10, "backup_count": 5}, "security": {"rate_limit_messages_per_minute": 30, "rate_limit_commands_per_minute": 10, "session_timeout_minutes": 60, "max_concurrent_sessions": 100}, "features": {"enable_message_history": true, "enable_user_sessions": true, "enable_command_logging": true, "enable_auto_responses": false, "enable_rich_formatting": true}, "notifications": {"default_enabled": true, "types": {"system": true, "alerts": true, "updates": true, "responses": true}, "quiet_hours": {"enabled": false, "start": "22:00", "end": "08:00", "timezone": "UTC"}}}