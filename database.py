"""
Database operations for the Telegram MCP Server.
Handles message storage, user sessions, and configuration persistence.
"""

import sqlite3
import aiosqlite
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class Message:
    """Represents a message in the system."""
    id: Optional[int] = None
    user_id: int = 0
    username: Optional[str] = None
    message_text: str = ""
    message_type: str = "text"  # text, command, notification, system
    direction: str = "incoming"  # incoming, outgoing
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    processed: bool = False


@dataclass
class UserSession:
    """Represents a user session."""
    user_id: int
    username: Optional[str] = None
    session_start: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    is_active: bool = True
    context: Optional[Dict[str, Any]] = None


class DatabaseManager:
    """Manages database operations for the Telegram MCP Server."""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection: Optional[aiosqlite.Connection] = None
    
    async def initialize(self):
        """Initialize the database and create tables."""
        self.connection = await aiosqlite.connect(self.db_path)
        await self._create_tables()
        logger.info(f"Database initialized at {self.db_path}")
    
    async def close(self):
        """Close the database connection."""
        if self.connection:
            await self.connection.close()
            logger.info("Database connection closed")
    
    async def _create_tables(self):
        """Create necessary database tables."""
        # Messages table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                username TEXT,
                message_text TEXT NOT NULL,
                message_type TEXT DEFAULT 'text',
                direction TEXT DEFAULT 'incoming',
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT,
                processed BOOLEAN DEFAULT FALSE
            )
        """)
        
        # User sessions table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                user_id INTEGER PRIMARY KEY,
                username TEXT,
                session_start DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                context TEXT
            )
        """)
        
        # Configuration table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS configuration (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes for better performance
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id)
        """)
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)
        """)
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_processed ON messages(processed)
        """)
        
        await self.connection.commit()
    
    async def save_message(self, message: Message) -> int:
        """Save a message to the database."""
        metadata_json = json.dumps(message.metadata) if message.metadata else None
        
        cursor = await self.connection.execute("""
            INSERT INTO messages (user_id, username, message_text, message_type, 
                                direction, metadata, processed)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            message.user_id,
            message.username,
            message.message_text,
            message.message_type,
            message.direction,
            metadata_json,
            message.processed
        ))
        
        await self.connection.commit()
        return cursor.lastrowid
    
    async def get_messages(self, user_id: Optional[int] = None, 
                          limit: int = 100, 
                          message_type: Optional[str] = None,
                          unprocessed_only: bool = False) -> List[Message]:
        """Retrieve messages from the database."""
        query = "SELECT * FROM messages WHERE 1=1"
        params = []
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if message_type:
            query += " AND message_type = ?"
            params.append(message_type)
        
        if unprocessed_only:
            query += " AND processed = FALSE"
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor = await self.connection.execute(query, params)
        rows = await cursor.fetchall()
        
        messages = []
        for row in rows:
            metadata = json.loads(row[7]) if row[7] else None
            message = Message(
                id=row[0],
                user_id=row[1],
                username=row[2],
                message_text=row[3],
                message_type=row[4],
                direction=row[5],
                timestamp=datetime.fromisoformat(row[6]) if row[6] else None,
                metadata=metadata,
                processed=bool(row[8])
            )
            messages.append(message)
        
        return messages
    
    async def mark_message_processed(self, message_id: int):
        """Mark a message as processed."""
        await self.connection.execute("""
            UPDATE messages SET processed = TRUE WHERE id = ?
        """, (message_id,))
        await self.connection.commit()
    
    async def create_or_update_session(self, session: UserSession):
        """Create or update a user session."""
        context_json = json.dumps(session.context) if session.context else None
        
        await self.connection.execute("""
            INSERT OR REPLACE INTO user_sessions 
            (user_id, username, session_start, last_activity, is_active, context)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            session.user_id,
            session.username,
            session.session_start or datetime.now(),
            session.last_activity or datetime.now(),
            session.is_active,
            context_json
        ))
        await self.connection.commit()
    
    async def get_session(self, user_id: int) -> Optional[UserSession]:
        """Get a user session."""
        cursor = await self.connection.execute("""
            SELECT * FROM user_sessions WHERE user_id = ?
        """, (user_id,))
        row = await cursor.fetchone()
        
        if not row:
            return None
        
        context = json.loads(row[5]) if row[5] else None
        return UserSession(
            user_id=row[0],
            username=row[1],
            session_start=datetime.fromisoformat(row[2]) if row[2] else None,
            last_activity=datetime.fromisoformat(row[3]) if row[3] else None,
            is_active=bool(row[4]),
            context=context
        )
    
    async def update_session_activity(self, user_id: int):
        """Update the last activity time for a user session."""
        await self.connection.execute("""
            UPDATE user_sessions 
            SET last_activity = CURRENT_TIMESTAMP 
            WHERE user_id = ?
        """, (user_id,))
        await self.connection.commit()
    
    async def get_active_users(self) -> List[int]:
        """Get list of active user IDs."""
        cursor = await self.connection.execute("""
            SELECT user_id FROM user_sessions 
            WHERE is_active = TRUE 
            AND last_activity > datetime('now', '-1 hour')
        """)
        rows = await cursor.fetchall()
        return [row[0] for row in rows]
    
    async def cleanup_old_messages(self, days_old: int = 30):
        """Clean up old messages."""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        cursor = await self.connection.execute("""
            DELETE FROM messages 
            WHERE timestamp < ? AND processed = TRUE
        """, (cutoff_date,))
        
        deleted_count = cursor.rowcount
        await self.connection.commit()
        
        logger.info(f"Cleaned up {deleted_count} old messages")
        return deleted_count
    
    async def set_config(self, key: str, value: Any):
        """Set a configuration value."""
        value_json = json.dumps(value)
        await self.connection.execute("""
            INSERT OR REPLACE INTO configuration (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        """, (key, value_json))
        await self.connection.commit()
    
    async def get_config(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        cursor = await self.connection.execute("""
            SELECT value FROM configuration WHERE key = ?
        """, (key,))
        row = await cursor.fetchone()
        
        if not row:
            return default
        
        return json.loads(row[0])
