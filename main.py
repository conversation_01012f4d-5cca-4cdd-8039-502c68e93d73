"""
Main application entry point for the Telegram MCP Server.
Coordinates the startup and shutdown of all components.
"""

import asyncio
import json
import logging
import signal
import sys
import os
from pathlib import Path
from typing import Optional

from database import DatabaseManager
from telegram_bot import TelegramBot
from message_bridge import MessageBridge

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('data/app.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)


class TelegramMCPApplication:
    """Main application class that coordinates all components."""
    
    def __init__(self, config_path: str = "config/config.json"):
        self.config_path = config_path
        self.config: Optional[dict] = None
        
        # Components
        self.db_manager: Optional[DatabaseManager] = None
        self.telegram_bot: Optional[TelegramBot] = None
        self.message_bridge: Optional[MessageBridge] = None
        
        # Application state
        self.is_running = False
        self.shutdown_event = asyncio.Event()
    
    async def load_config(self):
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
            logger.info(f"Configuration loaded from {self.config_path}")
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            logger.error("Please copy config/config.example.json to config/config.json and configure it")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            raise
    
    async def setup_directories(self):
        """Create necessary directories."""
        directories = ["data", "config", "logs"]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
        
        logger.info("Directory structure verified")
    
    async def initialize_database(self):
        """Initialize the database manager."""
        db_path = self.config["database"]["path"]
        
        # Ensure database directory exists
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        self.db_manager = DatabaseManager(db_path)
        await self.db_manager.initialize()
        
        logger.info("Database manager initialized")
    
    async def initialize_message_bridge(self):
        """Initialize the message bridge."""
        self.message_bridge = MessageBridge(self.db_manager, self.config)
        await self.message_bridge.initialize()
        
        logger.info("Message bridge initialized")
    
    async def initialize_telegram_bot(self):
        """Initialize the Telegram bot."""
        self.telegram_bot = TelegramBot(self.config)
        await self.telegram_bot.initialize(self.db_manager, self.message_bridge)
        
        # Set up message bridge callbacks
        self.message_bridge.set_telegram_sender(self.telegram_bot.send_message_to_user)
        
        logger.info("Telegram bot initialized")
    
    async def start_components(self):
        """Start all application components."""
        # Start Telegram bot
        await self.telegram_bot.start()
        
        logger.info("All components started successfully")
        self.is_running = True
    
    async def stop_components(self):
        """Stop all application components."""
        logger.info("Stopping application components...")
        
        # Stop Telegram bot
        if self.telegram_bot:
            await self.telegram_bot.stop()
        
        # Close message bridge
        if self.message_bridge:
            await self.message_bridge.close()
        
        # Close database
        if self.db_manager:
            await self.db_manager.close()
        
        self.is_running = False
        logger.info("All components stopped")
    
    async def run(self):
        """Run the main application."""
        try:
            # Setup
            await self.load_config()
            await self.setup_directories()
            
            # Initialize components
            await self.initialize_database()
            await self.initialize_message_bridge()
            await self.initialize_telegram_bot()
            
            # Start components
            await self.start_components()
            
            logger.info("🚀 Telegram MCP Server started successfully!")
            logger.info("Press Ctrl+C to stop the server")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"Application error: {e}")
            raise
        finally:
            await self.stop_components()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        self.shutdown_event.set()


async def main():
    """Main entry point."""
    # Create application instance
    app = TelegramMCPApplication()
    
    # Set up signal handlers
    def signal_handler(signum, frame):
        app.signal_handler(signum, frame)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await app.run()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
    
    logger.info("Application shutdown complete")


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 10):
        print("Error: Python 3.10 or higher is required")
        sys.exit(1)
    
    # Check for required configuration
    if not os.path.exists("config/config.json"):
        print("Error: Configuration file not found")
        print("Please copy config/config.example.json to config/config.json and configure it")
        sys.exit(1)
    
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
