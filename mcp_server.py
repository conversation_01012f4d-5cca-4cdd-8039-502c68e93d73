"""
MCP Server implementation for Telegram bot integration.
Provides tools for AI agents to interact with Telegram users.
"""

import asyncio
import json
import logging
from typing import Optional
from datetime import datetime

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Call<PERSON>oolR<PERSON>ult
from database import DatabaseManager, Message, UserSession
from message_bridge import MessageBridge

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize MCP server
server = Server("telegram-notifications")

# Global instances
db_manager: Optional[DatabaseManager] = None
message_bridge: Optional[MessageBridge] = None


async def initialize_server():
    """Initialize the MCP server with database and message bridge."""
    global db_manager, message_bridge
    
    # Load configuration
    try:
        with open("config/config.json", "r") as f:
            config = json.load(f)
    except FileNotFoundError:
        logger.error("Configuration file not found. Please copy config.example.json to config.json")
        raise
    
    # Initialize database
    db_manager = DatabaseManager(config["database"]["path"])
    await db_manager.initialize()
    
    # Initialize message bridge
    message_bridge = MessageBridge(db_manager, config)
    await message_bridge.initialize()
    
    logger.info("MCP server initialized successfully")


# Tool definitions
@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """List available tools."""
    return [
        Tool(
            name="send_telegram_message",
            description="Send a message to a Telegram user",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {"type": "integer", "description": "Telegram user ID"},
                    "message": {"type": "string", "description": "Message content"},
                    "message_type": {"type": "string", "description": "Type of message", "default": "notification"}
                },
                "required": ["user_id", "message"]
            }
        ),
        Tool(
            name="get_telegram_messages",
            description="Get recent Telegram messages",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {"type": "integer", "description": "Optional user ID filter"},
                    "limit": {"type": "integer", "description": "Maximum messages to retrieve", "default": 10},
                    "message_type": {"type": "string", "description": "Optional message type filter"},
                    "unprocessed_only": {"type": "boolean", "description": "Only unprocessed messages", "default": False}
                }
            }
        ),
        Tool(
            name="list_telegram_users",
            description="Get list of active Telegram users",
            inputSchema={"type": "object", "properties": {}}
        ),
        Tool(
            name="set_notification_preferences",
            description="Set notification preferences for a user",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {"type": "integer", "description": "Telegram user ID"},
                    "preferences": {"type": "object", "description": "Notification preferences"}
                },
                "required": ["user_id", "preferences"]
            }
        ),
        Tool(
            name="get_server_status",
            description="Get server status information",
            inputSchema={"type": "object", "properties": {}}
        ),
        Tool(
            name="mark_messages_processed",
            description="Mark messages as processed",
            inputSchema={
                "type": "object",
                "properties": {
                    "message_ids": {"type": "array", "items": {"type": "integer"}, "description": "Message IDs to mark"}
                },
                "required": ["message_ids"]
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> CallToolResult:
    """Handle tool calls."""
    try:
        if name == "send_telegram_message":
            return await send_telegram_message_impl(arguments)
        elif name == "get_telegram_messages":
            return await get_telegram_messages_impl(arguments)
        elif name == "list_telegram_users":
            return await list_telegram_users_impl(arguments)
        elif name == "set_notification_preferences":
            return await set_notification_preferences_impl(arguments)
        elif name == "get_server_status":
            return await get_server_status_impl(arguments)
        elif name == "mark_messages_processed":
            return await mark_messages_processed_impl(arguments)
        else:
            return CallToolResult(
                content=[TextContent(type="text", text=f"Unknown tool: {name}")]
            )
    except Exception as e:
        logger.error(f"Tool call error: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def send_telegram_message_impl(arguments: dict) -> CallToolResult:
    """Implementation for send_telegram_message tool."""
    user_id = arguments.get("user_id")
    message = arguments.get("message")
    message_type = arguments.get("message_type", "notification")
    try:
        if not message_bridge:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Message bridge not initialized")]
            )

        # Create message object
        msg = Message(
            user_id=user_id,
            message_text=message,
            message_type=message_type,
            direction="outgoing",
            timestamp=datetime.now(),
            processed=False
        )

        # Save to database
        message_id = await db_manager.save_message(msg)
        msg.id = message_id

        # Send via message bridge
        success = await message_bridge.send_to_telegram(msg)

        if success:
            await db_manager.mark_message_processed(message_id)
            result_text = f"Message sent successfully to user {user_id}"
        else:
            result_text = f"Failed to send message to user {user_id}"

        return CallToolResult(
            content=[TextContent(type="text", text=result_text)]
        )

    except Exception as e:
        logger.error(f"Error sending Telegram message: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def get_telegram_messages_impl(arguments: dict) -> CallToolResult:
    """Implementation for get_telegram_messages tool."""
    user_id = arguments.get("user_id")
    limit = arguments.get("limit", 10)
    message_type = arguments.get("message_type")
    unprocessed_only = arguments.get("unprocessed_only", False)

    try:
        if not db_manager:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Database not initialized")]
            )

        messages = await db_manager.get_messages(
            user_id=user_id,
            limit=limit,
            message_type=message_type,
            unprocessed_only=unprocessed_only
        )

        # Convert messages to dict format
        message_data = []
        for msg in messages:
            message_data.append({
                "id": msg.id,
                "user_id": msg.user_id,
                "username": msg.username,
                "message_text": msg.message_text,
                "message_type": msg.message_type,
                "direction": msg.direction,
                "timestamp": msg.timestamp.isoformat() if msg.timestamp else None,
                "processed": msg.processed,
                "metadata": msg.metadata
            })

        result_text = json.dumps(message_data, indent=2)
        return CallToolResult(
            content=[TextContent(type="text", text=result_text)]
        )

    except Exception as e:
        logger.error(f"Error retrieving messages: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def list_telegram_users_impl(_arguments: dict) -> CallToolResult:
    """Implementation for list_telegram_users tool."""
    try:
        if not db_manager:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Database not initialized")]
            )

        active_users = await db_manager.get_active_users()

        # Get session details for each user
        user_data = []
        for user_id in active_users:
            session = await db_manager.get_session(user_id)
            if session:
                user_data.append({
                    "user_id": session.user_id,
                    "username": session.username,
                    "session_start": session.session_start.isoformat() if session.session_start else None,
                    "last_activity": session.last_activity.isoformat() if session.last_activity else None,
                    "is_active": session.is_active
                })

        result_text = json.dumps(user_data, indent=2)
        return CallToolResult(
            content=[TextContent(type="text", text=result_text)]
        )

    except Exception as e:
        logger.error(f"Error listing users: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def set_notification_preferences_impl(arguments: dict) -> CallToolResult:
    """Implementation for set_notification_preferences tool."""
    user_id = arguments.get("user_id")
    preferences = arguments.get("preferences", {})

    try:
        if not db_manager:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Database not initialized")]
            )

        # Get or create user session
        session = await db_manager.get_session(user_id)
        if not session:
            session = UserSession(user_id=user_id)

        # Update context with preferences
        if not session.context:
            session.context = {}
        session.context["notification_preferences"] = preferences

        # Save session
        await db_manager.create_or_update_session(session)

        result_text = f"Notification preferences updated for user {user_id}"
        return CallToolResult(
            content=[TextContent(type="text", text=result_text)]
        )

    except Exception as e:
        logger.error(f"Error setting notification preferences: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def get_server_status_impl(_arguments: dict) -> CallToolResult:
    """Implementation for get_server_status tool."""
    try:
        status = {
            "server_name": "telegram-notifications",
            "version": "1.0.0",
            "status": "running",
            "timestamp": datetime.now().isoformat(),
            "database_connected": db_manager is not None,
            "message_bridge_connected": message_bridge is not None and message_bridge.is_connected(),
            "active_users": len(await db_manager.get_active_users()) if db_manager else 0
        }

        result_text = json.dumps(status, indent=2)
        return CallToolResult(
            content=[TextContent(type="text", text=result_text)]
        )

    except Exception as e:
        logger.error(f"Error getting server status: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def mark_messages_processed_impl(arguments: dict) -> CallToolResult:
    """Implementation for mark_messages_processed tool."""
    message_ids = arguments.get("message_ids", [])

    try:
        if not db_manager:
            return CallToolResult(
                content=[TextContent(type="text", text="Error: Database not initialized")]
            )

        processed_count = 0
        for message_id in message_ids:
            await db_manager.mark_message_processed(message_id)
            processed_count += 1

        result_text = f"Marked {processed_count} messages as processed"
        return CallToolResult(
            content=[TextContent(type="text", text=result_text)]
        )

    except Exception as e:
        logger.error(f"Error marking messages as processed: {e}")
        return CallToolResult(
            content=[TextContent(type="text", text=f"Error: {str(e)}")]
        )


async def cleanup_handler():
    """Cleanup handler for graceful shutdown."""
    global db_manager, message_bridge
    
    if message_bridge:
        await message_bridge.close()
    
    if db_manager:
        await db_manager.close()
    
    logger.info("MCP server cleanup completed")


if __name__ == "__main__":
    async def main():
        try:
            await initialize_server()

            # Run the MCP server with stdio transport
            async with stdio_server() as (read_stream, write_stream):
                await server.run(
                    read_stream,
                    write_stream,
                    server.create_initialization_options()
                )

        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            await cleanup_handler()

    asyncio.run(main())
