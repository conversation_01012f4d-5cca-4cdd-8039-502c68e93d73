"""
MCP Server implementation for Telegram bot integration.
Provides tools for AI agents to interact with Telegram users.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from mcp.server.fastmcp import FastMCP
from database import DatabaseManager, Message, UserSession
from message_bridge import MessageBridge

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("telegram-notifications")

# Global instances
db_manager: Optional[DatabaseManager] = None
message_bridge: Optional[MessageBridge] = None


async def initialize_server():
    """Initialize the MCP server with database and message bridge."""
    global db_manager, message_bridge
    
    # Load configuration
    try:
        with open("config/config.json", "r") as f:
            config = json.load(f)
    except FileNotFoundError:
        logger.error("Configuration file not found. Please copy config.example.json to config.json")
        raise
    
    # Initialize database
    db_manager = DatabaseManager(config["database"]["path"])
    await db_manager.initialize()
    
    # Initialize message bridge
    message_bridge = MessageBridge(db_manager, config)
    await message_bridge.initialize()
    
    logger.info("MCP server initialized successfully")


@mcp.tool()
async def send_telegram_message(user_id: int, message: str, 
                               message_type: str = "notification") -> str:
    """Send a message to a Telegram user.
    
    Args:
        user_id: Telegram user ID to send message to
        message: Message content to send
        message_type: Type of message (notification, alert, response, etc.)
    
    Returns:
        Status message indicating success or failure
    """
    try:
        if not message_bridge:
            return "Error: Message bridge not initialized"
        
        # Create message object
        msg = Message(
            user_id=user_id,
            message_text=message,
            message_type=message_type,
            direction="outgoing",
            timestamp=datetime.now(),
            processed=False
        )
        
        # Save to database
        message_id = await db_manager.save_message(msg)
        msg.id = message_id
        
        # Send via message bridge
        success = await message_bridge.send_to_telegram(msg)
        
        if success:
            await db_manager.mark_message_processed(message_id)
            return f"Message sent successfully to user {user_id}"
        else:
            return f"Failed to send message to user {user_id}"
    
    except Exception as e:
        logger.error(f"Error sending Telegram message: {e}")
        return f"Error: {str(e)}"


@mcp.tool()
async def get_telegram_messages(user_id: Optional[int] = None, 
                               limit: int = 10,
                               message_type: Optional[str] = None,
                               unprocessed_only: bool = False) -> str:
    """Get recent Telegram messages.
    
    Args:
        user_id: Optional user ID to filter messages
        limit: Maximum number of messages to retrieve
        message_type: Optional message type filter
        unprocessed_only: Only return unprocessed messages
    
    Returns:
        JSON string containing message data
    """
    try:
        if not db_manager:
            return "Error: Database not initialized"
        
        messages = await db_manager.get_messages(
            user_id=user_id,
            limit=limit,
            message_type=message_type,
            unprocessed_only=unprocessed_only
        )
        
        # Convert messages to dict format
        message_data = []
        for msg in messages:
            message_data.append({
                "id": msg.id,
                "user_id": msg.user_id,
                "username": msg.username,
                "message_text": msg.message_text,
                "message_type": msg.message_type,
                "direction": msg.direction,
                "timestamp": msg.timestamp.isoformat() if msg.timestamp else None,
                "processed": msg.processed,
                "metadata": msg.metadata
            })
        
        return json.dumps(message_data, indent=2)
    
    except Exception as e:
        logger.error(f"Error retrieving messages: {e}")
        return f"Error: {str(e)}"


@mcp.tool()
async def list_telegram_users() -> str:
    """Get list of active Telegram users.
    
    Returns:
        JSON string containing active user data
    """
    try:
        if not db_manager:
            return "Error: Database not initialized"
        
        active_users = await db_manager.get_active_users()
        
        # Get session details for each user
        user_data = []
        for user_id in active_users:
            session = await db_manager.get_session(user_id)
            if session:
                user_data.append({
                    "user_id": session.user_id,
                    "username": session.username,
                    "session_start": session.session_start.isoformat() if session.session_start else None,
                    "last_activity": session.last_activity.isoformat() if session.last_activity else None,
                    "is_active": session.is_active
                })
        
        return json.dumps(user_data, indent=2)
    
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        return f"Error: {str(e)}"


@mcp.tool()
async def set_notification_preferences(user_id: int, preferences: Dict[str, Any]) -> str:
    """Set notification preferences for a user.
    
    Args:
        user_id: Telegram user ID
        preferences: Dictionary of notification preferences
    
    Returns:
        Status message
    """
    try:
        if not db_manager:
            return "Error: Database not initialized"
        
        # Get or create user session
        session = await db_manager.get_session(user_id)
        if not session:
            session = UserSession(user_id=user_id)
        
        # Update context with preferences
        if not session.context:
            session.context = {}
        session.context["notification_preferences"] = preferences
        
        # Save session
        await db_manager.create_or_update_session(session)
        
        return f"Notification preferences updated for user {user_id}"
    
    except Exception as e:
        logger.error(f"Error setting notification preferences: {e}")
        return f"Error: {str(e)}"


@mcp.tool()
async def get_server_status() -> str:
    """Get the current status of the Telegram MCP server.
    
    Returns:
        JSON string containing server status information
    """
    try:
        status = {
            "server_name": "telegram-notifications",
            "version": "1.0.0",
            "status": "running",
            "timestamp": datetime.now().isoformat(),
            "database_connected": db_manager is not None,
            "message_bridge_connected": message_bridge is not None and message_bridge.is_connected(),
            "active_users": len(await db_manager.get_active_users()) if db_manager else 0
        }
        
        return json.dumps(status, indent=2)
    
    except Exception as e:
        logger.error(f"Error getting server status: {e}")
        return f"Error: {str(e)}"


@mcp.tool()
async def mark_messages_processed(message_ids: List[int]) -> str:
    """Mark multiple messages as processed.
    
    Args:
        message_ids: List of message IDs to mark as processed
    
    Returns:
        Status message
    """
    try:
        if not db_manager:
            return "Error: Database not initialized"
        
        processed_count = 0
        for message_id in message_ids:
            await db_manager.mark_message_processed(message_id)
            processed_count += 1
        
        return f"Marked {processed_count} messages as processed"
    
    except Exception as e:
        logger.error(f"Error marking messages as processed: {e}")
        return f"Error: {str(e)}"


async def cleanup_handler():
    """Cleanup handler for graceful shutdown."""
    global db_manager, message_bridge
    
    if message_bridge:
        await message_bridge.close()
    
    if db_manager:
        await db_manager.close()
    
    logger.info("MCP server cleanup completed")


if __name__ == "__main__":
    async def main():
        try:
            await initialize_server()
            
            # Run the MCP server
            mcp.run(transport='stdio')
            
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            await cleanup_handler()
    
    asyncio.run(main())
