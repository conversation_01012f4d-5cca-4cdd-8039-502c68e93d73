"""
Message Bridge for coordinating communication between MCP server and Telegram bot.
Handles message routing, queuing, and format conversion.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass
from queue import Queue
import threading

from database import DatabaseManager, Message

logger = logging.getLogger(__name__)


@dataclass
class QueuedMessage:
    """Represents a message in the processing queue."""
    message: Message
    destination: str  # 'telegram' or 'mcp'
    priority: int = 1  # Higher number = higher priority
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class MessageBridge:
    """Coordinates message flow between MCP server and Telegram bot."""
    
    def __init__(self, db_manager: DatabaseManager, config: Dict[str, Any]):
        self.db_manager = db_manager
        self.config = config
        
        # Message queues
        self.telegram_queue: asyncio.Queue[QueuedMessage] = asyncio.Queue()
        self.mcp_queue: asyncio.Queue[QueuedMessage] = asyncio.Queue()
        
        # Callback functions
        self.telegram_sender: Optional[Callable] = None
        self.mcp_sender: Optional[Callable] = None
        
        # Processing control
        self.is_running = False
        self.processing_tasks: List[asyncio.Task] = []
        
        # Statistics
        self.stats = {
            "messages_processed": 0,
            "messages_failed": 0,
            "telegram_messages_sent": 0,
            "mcp_messages_sent": 0,
            "last_activity": None
        }
    
    async def initialize(self):
        """Initialize the message bridge."""
        self.is_running = True
        
        # Start processing tasks
        self.processing_tasks = [
            asyncio.create_task(self._process_telegram_queue()),
            asyncio.create_task(self._process_mcp_queue()),
            asyncio.create_task(self._cleanup_old_messages())
        ]
        
        logger.info("Message bridge initialized")
    
    async def close(self):
        """Close the message bridge and cleanup resources."""
        self.is_running = False
        
        # Cancel processing tasks
        for task in self.processing_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.processing_tasks, return_exceptions=True)
        
        logger.info("Message bridge closed")
    
    def set_telegram_sender(self, sender_func: Callable):
        """Set the function to send messages to Telegram."""
        self.telegram_sender = sender_func
    
    def set_mcp_sender(self, sender_func: Callable):
        """Set the function to send messages to MCP."""
        self.mcp_sender = sender_func
    
    def is_connected(self) -> bool:
        """Check if the bridge is properly connected."""
        return (self.is_running and 
                self.telegram_sender is not None and 
                self.mcp_sender is not None)
    
    async def send_to_telegram(self, message: Message) -> bool:
        """Queue a message to be sent to Telegram."""
        try:
            queued_msg = QueuedMessage(
                message=message,
                destination="telegram",
                priority=2 if message.message_type in ["alert", "notification"] else 1
            )
            
            await self.telegram_queue.put(queued_msg)
            logger.debug(f"Queued message for Telegram: {message.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue message for Telegram: {e}")
            return False
    
    async def send_to_mcp(self, message: Message) -> bool:
        """Queue a message to be sent to MCP."""
        try:
            queued_msg = QueuedMessage(
                message=message,
                destination="mcp",
                priority=2 if message.message_type in ["command", "question"] else 1
            )
            
            await self.mcp_queue.put(queued_msg)
            logger.debug(f"Queued message for MCP: {message.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue message for MCP: {e}")
            return False
    
    async def _process_telegram_queue(self):
        """Process messages queued for Telegram."""
        while self.is_running:
            try:
                # Wait for message with timeout
                queued_msg = await asyncio.wait_for(
                    self.telegram_queue.get(), 
                    timeout=1.0
                )
                
                success = await self._send_telegram_message(queued_msg)
                
                if success:
                    self.stats["telegram_messages_sent"] += 1
                    self.stats["messages_processed"] += 1
                    await self.db_manager.mark_message_processed(queued_msg.message.id)
                else:
                    await self._handle_failed_message(queued_msg, "telegram")
                
                self.stats["last_activity"] = datetime.now()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing Telegram queue: {e}")
                await asyncio.sleep(1)
    
    async def _process_mcp_queue(self):
        """Process messages queued for MCP."""
        while self.is_running:
            try:
                # Wait for message with timeout
                queued_msg = await asyncio.wait_for(
                    self.mcp_queue.get(), 
                    timeout=1.0
                )
                
                success = await self._send_mcp_message(queued_msg)
                
                if success:
                    self.stats["mcp_messages_sent"] += 1
                    self.stats["messages_processed"] += 1
                    await self.db_manager.mark_message_processed(queued_msg.message.id)
                else:
                    await self._handle_failed_message(queued_msg, "mcp")
                
                self.stats["last_activity"] = datetime.now()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing MCP queue: {e}")
                await asyncio.sleep(1)
    
    async def _send_telegram_message(self, queued_msg: QueuedMessage) -> bool:
        """Send a message to Telegram."""
        if not self.telegram_sender:
            logger.error("Telegram sender not configured")
            return False
        
        try:
            message = queued_msg.message
            
            # Format message for Telegram
            formatted_text = self._format_message_for_telegram(message)
            
            # Send via Telegram sender
            success = await self.telegram_sender(
                message.user_id, 
                formatted_text
            )
            
            if success:
                logger.debug(f"Sent message {message.id} to Telegram user {message.user_id}")
            else:
                logger.warning(f"Failed to send message {message.id} to Telegram")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending message to Telegram: {e}")
            return False
    
    async def _send_mcp_message(self, queued_msg: QueuedMessage) -> bool:
        """Send a message to MCP."""
        if not self.mcp_sender:
            logger.error("MCP sender not configured")
            return False
        
        try:
            message = queued_msg.message
            
            # Format message for MCP
            formatted_data = self._format_message_for_mcp(message)
            
            # Send via MCP sender
            success = await self.mcp_sender(formatted_data)
            
            if success:
                logger.debug(f"Sent message {message.id} to MCP")
            else:
                logger.warning(f"Failed to send message {message.id} to MCP")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending message to MCP: {e}")
            return False
    
    def _format_message_for_telegram(self, message: Message) -> str:
        """Format a message for Telegram display."""
        # Add emoji based on message type
        emoji_map = {
            "notification": "🔔",
            "alert": "⚠️",
            "response": "🤖",
            "system": "⚙️",
            "error": "❌"
        }
        
        emoji = emoji_map.get(message.message_type, "💬")
        
        # Format timestamp
        timestamp = ""
        if message.timestamp:
            timestamp = message.timestamp.strftime("%H:%M:%S")
        
        # Build formatted message
        if message.message_type in ["notification", "alert", "system"]:
            formatted = f"{emoji} **{message.message_type.title()}**\n{message.message_text}"
        elif message.message_type == "response":
            formatted = f"{emoji} **AI Response:**\n{message.message_text}"
        else:
            formatted = f"{emoji} {message.message_text}"
        
        # Add timestamp if available
        if timestamp:
            formatted += f"\n\n_Sent at {timestamp}_"
        
        return formatted
    
    def _format_message_for_mcp(self, message: Message) -> Dict[str, Any]:
        """Format a message for MCP processing."""
        return {
            "id": message.id,
            "user_id": message.user_id,
            "username": message.username,
            "text": message.message_text,
            "type": message.message_type,
            "timestamp": message.timestamp.isoformat() if message.timestamp else None,
            "metadata": message.metadata or {}
        }
    
    async def _handle_failed_message(self, queued_msg: QueuedMessage, destination: str):
        """Handle a failed message delivery."""
        queued_msg.retry_count += 1
        
        if queued_msg.retry_count <= queued_msg.max_retries:
            # Retry with exponential backoff
            delay = 2 ** queued_msg.retry_count
            await asyncio.sleep(delay)
            
            # Re-queue the message
            if destination == "telegram":
                await self.telegram_queue.put(queued_msg)
            else:
                await self.mcp_queue.put(queued_msg)
            
            logger.info(f"Retrying message {queued_msg.message.id} (attempt {queued_msg.retry_count})")
        else:
            # Max retries exceeded
            self.stats["messages_failed"] += 1
            logger.error(f"Message {queued_msg.message.id} failed after {queued_msg.max_retries} retries")
            
            # Save failure to database
            await self._save_failed_message(queued_msg, destination)
    
    async def _save_failed_message(self, queued_msg: QueuedMessage, destination: str):
        """Save information about a failed message."""
        try:
            failure_info = {
                "original_message_id": queued_msg.message.id,
                "destination": destination,
                "retry_count": queued_msg.retry_count,
                "failure_time": datetime.now().isoformat(),
                "error_type": "max_retries_exceeded"
            }
            
            await self.db_manager.set_config(
                f"failed_message_{queued_msg.message.id}_{destination}",
                failure_info
            )
            
        except Exception as e:
            logger.error(f"Failed to save failure info: {e}")
    
    async def _cleanup_old_messages(self):
        """Periodically cleanup old messages and statistics."""
        while self.is_running:
            try:
                # Sleep for cleanup interval
                cleanup_hours = self.config.get("database", {}).get("cleanup_interval_hours", 24)
                await asyncio.sleep(cleanup_hours * 3600)
                
                # Cleanup old messages
                await self.db_manager.cleanup_old_messages(days_old=30)
                
                logger.info("Completed periodic message cleanup")
                
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying
    
    def get_stats(self) -> Dict[str, Any]:
        """Get bridge statistics."""
        return {
            **self.stats,
            "telegram_queue_size": self.telegram_queue.qsize(),
            "mcp_queue_size": self.mcp_queue.qsize(),
            "is_running": self.is_running,
            "is_connected": self.is_connected()
        }
    
    async def broadcast_message(self, message_text: str, message_type: str = "notification") -> int:
        """Broadcast a message to all active users."""
        if not self.db_manager:
            return 0
        
        active_users = await self.db_manager.get_active_users()
        sent_count = 0
        
        for user_id in active_users:
            message = Message(
                user_id=user_id,
                message_text=message_text,
                message_type=message_type,
                direction="outgoing",
                timestamp=datetime.now()
            )
            
            # Save message
            message_id = await self.db_manager.save_message(message)
            message.id = message_id
            
            # Queue for sending
            success = await self.send_to_telegram(message)
            if success:
                sent_count += 1
        
        logger.info(f"Broadcast message queued for {sent_count} users")
        return sent_count
