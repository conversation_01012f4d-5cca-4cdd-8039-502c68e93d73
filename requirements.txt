# MCP (Model Context Protocol) dependencies
mcp>=1.2.0
fastmcp>=0.1.0

# Telegram bot dependencies
python-telegram-bot>=21.0.0
telegram>=0.0.1

# Database and storage
sqlite3
aiosqlite>=0.19.0

# Async and networking
asyncio
aiohttp>=3.9.0
httpx>=0.25.0

# Configuration and utilities
pydantic>=2.5.0
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Logging and monitoring
structlog>=23.2.0
rich>=13.7.0

# Testing (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0

# Development tools (optional)
black>=23.12.0
isort>=5.13.0
mypy>=1.8.0
