"""
Setup script for the Telegram MCP Server.
Handles initial configuration and dependency installation.
"""

import os
import sys
import json
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 10):
        print("❌ Error: Python 3.10 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    directories = ["data", "config", "logs"]
    
    print("📁 Creating directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✅ {directory}/")
    
    return True


def setup_configuration():
    """Set up configuration file."""
    config_path = "config/config.json"
    example_path = "config/config.example.json"
    
    if os.path.exists(config_path):
        print("⚠️  Configuration file already exists")
        response = input("Do you want to overwrite it? (y/N): ").lower()
        if response != 'y':
            print("📝 Keeping existing configuration")
            return True
    
    if not os.path.exists(example_path):
        print(f"❌ Example configuration file not found: {example_path}")
        return False
    
    # Copy example to config
    with open(example_path, 'r') as f:
        config = json.load(f)
    
    print("🔧 Setting up configuration...")
    
    # Get Telegram bot token
    bot_token = input("Enter your Telegram bot token (from @BotFather): ").strip()
    if not bot_token:
        print("❌ Bot token is required")
        return False
    
    config["telegram"]["bot_token"] = bot_token
    
    # Get allowed user IDs
    print("\nEnter allowed user IDs (comma-separated):")
    print("You can get your user ID by messaging @userinfobot on Telegram")
    user_ids_input = input("User IDs: ").strip()
    
    if user_ids_input:
        try:
            user_ids = [int(uid.strip()) for uid in user_ids_input.split(",")]
            config["telegram"]["allowed_users"] = user_ids
            config["telegram"]["admin_users"] = user_ids  # Make all users admin by default
        except ValueError:
            print("❌ Invalid user IDs format")
            return False
    
    # Save configuration
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration saved to {config_path}")
    return True


def setup_claude_desktop():
    """Provide instructions for Claude Desktop setup."""
    print("\n🤖 Claude Desktop Setup Instructions:")
    print("=" * 50)
    
    current_dir = os.path.abspath(".")
    mcp_server_path = os.path.join(current_dir, "mcp_server.py")
    
    claude_config = {
        "mcpServers": {
            "telegram-notifications": {
                "command": "python",
                "args": [mcp_server_path]
            }
        }
    }
    
    print("Add the following to your Claude Desktop configuration:")
    print("(Usually located at ~/Library/Application Support/Claude/claude_desktop_config.json)")
    print()
    print(json.dumps(claude_config, indent=2))
    print()
    print("After adding this configuration:")
    print("1. Restart Claude Desktop")
    print("2. Look for the tools icon in Claude Desktop")
    print("3. You should see 'telegram-notifications' tools available")


def run_tests():
    """Run basic tests to verify setup."""
    print("\n🧪 Running basic tests...")
    
    # Test configuration loading
    try:
        with open("config/config.json", 'r') as f:
            config = json.load(f)
        print("✅ Configuration file is valid JSON")
    except Exception as e:
        print(f"❌ Configuration file error: {e}")
        return False
    
    # Test database creation
    try:
        import sqlite3
        db_path = config["database"]["path"]
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        conn.close()
        print("✅ Database connection test passed")
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    
    # Test imports
    try:
        import telegram
        import mcp
        print("✅ Required packages import successfully")
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False
    
    return True


def main():
    """Main setup function."""
    print("🚀 Telegram MCP Server Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Create directories
    if not create_directories():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Setup configuration
    if not setup_configuration():
        return False
    
    # Run tests
    if not run_tests():
        print("⚠️  Some tests failed, but you can still try running the server")
    
    # Show Claude Desktop setup
    setup_claude_desktop()
    
    print("\n🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Configure Claude Desktop (see instructions above)")
    print("2. Run the server: python main.py")
    print("3. Test by messaging your Telegram bot")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
