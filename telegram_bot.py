"""
Telegram Bot implementation for MCP integration.
Handles Telegram API interactions and user commands.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from telegram import Update, Bot
from telegram.ext import (
    Application, CommandHandler, MessageHandler, 
    ContextTypes, filters
)

from database import DatabaseManager, Message, UserSession
from message_bridge import MessageBridge

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TelegramBot:
    """Telegram bot for MCP integration."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.bot_token = config["telegram"]["bot_token"]
        self.allowed_users = set(config["telegram"]["allowed_users"])
        self.admin_users = set(config["telegram"]["admin_users"])
        
        self.application: Optional[Application] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.message_bridge: Optional[MessageBridge] = None
        
        # Rate limiting
        self.user_message_counts: Dict[int, List[datetime]] = {}
        self.rate_limit_messages = config["security"]["rate_limit_messages_per_minute"]
        self.rate_limit_commands = config["security"]["rate_limit_commands_per_minute"]
    
    async def initialize(self, db_manager: DatabaseManager, message_bridge: MessageBridge):
        """Initialize the Telegram bot."""
        self.db_manager = db_manager
        self.message_bridge = message_bridge
        
        # Create application
        self.application = Application.builder().token(self.bot_token).build()
        
        # Add handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("history", self.history_command))
        self.application.add_handler(CommandHandler("ask", self.ask_command))
        self.application.add_handler(CommandHandler("notify", self.notify_command))
        
        # Handle all text messages
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )
        
        logger.info("Telegram bot initialized")
    
    async def start(self):
        """Start the Telegram bot."""
        if not self.application:
            raise RuntimeError("Bot not initialized")
        
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        logger.info("Telegram bot started")
    
    async def stop(self):
        """Stop the Telegram bot."""
        if self.application:
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()
        
        logger.info("Telegram bot stopped")
    
    def _is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized."""
        return user_id in self.allowed_users
    
    def _is_admin(self, user_id: int) -> bool:
        """Check if user is admin."""
        return user_id in self.admin_users
    
    def _check_rate_limit(self, user_id: int, is_command: bool = False) -> bool:
        """Check if user is within rate limits."""
        now = datetime.now()
        limit = self.rate_limit_commands if is_command else self.rate_limit_messages
        
        if user_id not in self.user_message_counts:
            self.user_message_counts[user_id] = []
        
        # Remove old entries (older than 1 minute)
        self.user_message_counts[user_id] = [
            timestamp for timestamp in self.user_message_counts[user_id]
            if (now - timestamp).total_seconds() < 60
        ]
        
        # Check if under limit
        if len(self.user_message_counts[user_id]) >= limit:
            return False
        
        # Add current timestamp
        self.user_message_counts[user_id].append(now)
        return True
    
    async def _save_message(self, update: Update, message_type: str = "text", 
                           direction: str = "incoming") -> Optional[int]:
        """Save message to database."""
        if not self.db_manager or not update.message:
            return None
        
        message = Message(
            user_id=update.effective_user.id,
            username=update.effective_user.username,
            message_text=update.message.text or "",
            message_type=message_type,
            direction=direction,
            timestamp=datetime.now(),
            metadata={
                "chat_id": update.effective_chat.id,
                "message_id": update.message.message_id
            }
        )
        
        return await self.db_manager.save_message(message)
    
    async def _update_user_session(self, user_id: int, username: Optional[str] = None):
        """Update user session activity."""
        if not self.db_manager:
            return
        
        session = await self.db_manager.get_session(user_id)
        if not session:
            session = UserSession(
                user_id=user_id,
                username=username,
                session_start=datetime.now()
            )
        
        session.last_activity = datetime.now()
        session.is_active = True
        
        await self.db_manager.create_or_update_session(session)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            await update.message.reply_text("❌ You are not authorized to use this bot.")
            return
        
        if not self._check_rate_limit(user_id, is_command=True):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        await self._save_message(update, "command")
        await self._update_user_session(user_id, update.effective_user.username)
        
        welcome_text = """
🤖 **Telegram MCP Bot**

Welcome! This bot connects you to AI agents through the Model Context Protocol.

**Available Commands:**
• `/help` - Show this help message
• `/status` - Check connection status
• `/history` - View recent messages
• `/ask <question>` - Ask the AI agent a question
• `/notify <message>` - Send a notification (admin only)

You can also send regular messages that will be forwarded to the AI agent.
        """
        
        await update.message.reply_text(welcome_text, parse_mode='Markdown')
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            return
        
        if not self._check_rate_limit(user_id, is_command=True):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        await self._save_message(update, "command")
        
        help_text = """
🔧 **Bot Commands:**

**General Commands:**
• `/start` - Initialize the bot
• `/help` - Show this help message
• `/status` - Check system status
• `/history [limit]` - View recent messages

**AI Interaction:**
• `/ask <question>` - Ask the AI agent a question
• Send any message - Will be forwarded to AI agent

**Admin Commands:**
• `/notify <user_id> <message>` - Send notification to user

**Tips:**
• Messages are processed asynchronously
• Check `/status` if experiencing issues
• Use `/history` to see recent activity
        """
        
        await update.message.reply_text(help_text, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            return
        
        if not self._check_rate_limit(user_id, is_command=True):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        await self._save_message(update, "command")
        
        # Get system status
        status_text = "📊 **System Status:**\n\n"
        
        # Database status
        db_status = "✅ Connected" if self.db_manager else "❌ Disconnected"
        status_text += f"Database: {db_status}\n"
        
        # Message bridge status
        bridge_status = "✅ Connected" if (self.message_bridge and 
                                         self.message_bridge.is_connected()) else "❌ Disconnected"
        status_text += f"MCP Bridge: {bridge_status}\n"
        
        # User session info
        if self.db_manager:
            active_users = await self.db_manager.get_active_users()
            status_text += f"Active Users: {len(active_users)}\n"
        
        status_text += f"\nTimestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        await update.message.reply_text(status_text, parse_mode='Markdown')
    
    async def history_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /history command."""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            return
        
        if not self._check_rate_limit(user_id, is_command=True):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        await self._save_message(update, "command")
        
        # Parse limit from command
        limit = 5
        if context.args and len(context.args) > 0:
            try:
                limit = min(int(context.args[0]), 20)  # Max 20 messages
            except ValueError:
                await update.message.reply_text("❌ Invalid limit. Using default (5).")
        
        if not self.db_manager:
            await update.message.reply_text("❌ Database not available.")
            return
        
        # Get recent messages for this user
        messages = await self.db_manager.get_messages(user_id=user_id, limit=limit)
        
        if not messages:
            await update.message.reply_text("📭 No recent messages found.")
            return
        
        history_text = f"📜 **Recent Messages (Last {len(messages)}):**\n\n"
        
        for msg in reversed(messages):  # Show oldest first
            timestamp = msg.timestamp.strftime('%H:%M:%S') if msg.timestamp else "Unknown"
            direction_icon = "➡️" if msg.direction == "outgoing" else "⬅️"
            
            # Truncate long messages
            text = msg.message_text[:100] + "..." if len(msg.message_text) > 100 else msg.message_text
            
            history_text += f"{direction_icon} `{timestamp}` {text}\n"
        
        await update.message.reply_text(history_text, parse_mode='Markdown')
    
    async def ask_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /ask command."""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            return
        
        if not self._check_rate_limit(user_id, is_command=True):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        if not context.args:
            await update.message.reply_text("❌ Please provide a question. Usage: `/ask <your question>`")
            return
        
        question = " ".join(context.args)
        
        # Save the command
        await self._save_message(update, "command")
        
        # Create a message for the AI agent
        message = Message(
            user_id=user_id,
            username=update.effective_user.username,
            message_text=f"Question: {question}",
            message_type="question",
            direction="incoming",
            timestamp=datetime.now(),
            metadata={
                "chat_id": update.effective_chat.id,
                "command": "ask"
            }
        )
        
        # Save and forward to MCP
        message_id = await self.db_manager.save_message(message)
        message.id = message_id
        
        # Send to message bridge for AI processing
        if self.message_bridge:
            await self.message_bridge.send_to_mcp(message)
            await update.message.reply_text("🤔 Processing your question...")
        else:
            await update.message.reply_text("❌ AI agent connection not available.")
    
    async def notify_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /notify command (admin only)."""
        user_id = update.effective_user.id
        
        if not self._is_admin(user_id):
            await update.message.reply_text("❌ Admin access required.")
            return
        
        if not self._check_rate_limit(user_id, is_command=True):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        if len(context.args) < 2:
            await update.message.reply_text(
                "❌ Usage: `/notify <user_id> <message>`"
            )
            return
        
        try:
            target_user_id = int(context.args[0])
            notification_text = " ".join(context.args[1:])
        except ValueError:
            await update.message.reply_text("❌ Invalid user ID.")
            return
        
        # Save the command
        await self._save_message(update, "command")
        
        # Send notification
        try:
            await self.application.bot.send_message(
                chat_id=target_user_id,
                text=f"🔔 **Notification:**\n{notification_text}",
                parse_mode='Markdown'
            )
            await update.message.reply_text(f"✅ Notification sent to user {target_user_id}")
        except Exception as e:
            await update.message.reply_text(f"❌ Failed to send notification: {str(e)}")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle regular text messages."""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            return
        
        if not self._check_rate_limit(user_id):
            await update.message.reply_text("⚠️ Rate limit exceeded. Please wait.")
            return
        
        # Save message and update session
        await self._save_message(update, "text")
        await self._update_user_session(user_id, update.effective_user.username)
        
        # Create message for AI agent
        message = Message(
            user_id=user_id,
            username=update.effective_user.username,
            message_text=update.message.text,
            message_type="text",
            direction="incoming",
            timestamp=datetime.now(),
            metadata={
                "chat_id": update.effective_chat.id,
                "message_id": update.message.message_id
            }
        )
        
        # Save and forward to MCP
        message_id = await self.db_manager.save_message(message)
        message.id = message_id
        
        # Send to message bridge for AI processing
        if self.message_bridge:
            await self.message_bridge.send_to_mcp(message)
            # Send acknowledgment
            await update.message.reply_text("📨 Message received and forwarded to AI agent.")
        else:
            await update.message.reply_text("❌ AI agent connection not available.")
    
    async def send_message_to_user(self, user_id: int, message: str, 
                                  parse_mode: Optional[str] = 'Markdown') -> bool:
        """Send a message to a specific user."""
        try:
            await self.application.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=parse_mode
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send message to user {user_id}: {e}")
            return False
