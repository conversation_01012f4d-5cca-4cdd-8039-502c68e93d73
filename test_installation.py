"""
Test script to verify the Telegram MCP Server installation.
"""

import asyncio
import json
import os
import sys
from pathlib import Path


async def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")
    
    required_modules = [
        "telegram",
        "mcp",
        "aiosqlite",
        "asyncio",
        "json",
        "logging"
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All imports successful")
    return True


async def test_configuration():
    """Test configuration file."""
    print("\n🔍 Testing configuration...")
    
    config_path = "config/config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        print("Run: python setup.py")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        print("✅ Configuration file is valid JSON")
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in configuration: {e}")
        return False
    
    # Check required fields
    required_fields = [
        ("telegram", "bot_token"),
        ("telegram", "allowed_users"),
        ("database", "path"),
        ("mcp", "server_name")
    ]
    
    for section, field in required_fields:
        if section not in config:
            print(f"❌ Missing configuration section: {section}")
            return False
        
        if field not in config[section]:
            print(f"❌ Missing configuration field: {section}.{field}")
            return False
        
        if not config[section][field]:
            print(f"❌ Empty configuration field: {section}.{field}")
            return False
    
    print("✅ Configuration validation passed")
    return True


async def test_database():
    """Test database operations."""
    print("\n🔍 Testing database...")
    
    try:
        from database import DatabaseManager
        
        # Use test database
        test_db_path = "data/test.db"
        Path(test_db_path).parent.mkdir(parents=True, exist_ok=True)
        
        db_manager = DatabaseManager(test_db_path)
        await db_manager.initialize()
        
        print("✅ Database initialization successful")
        
        # Test basic operations
        from database import Message
        from datetime import datetime
        
        test_message = Message(
            user_id=123456789,
            username="test_user",
            message_text="Test message",
            message_type="test",
            direction="incoming",
            timestamp=datetime.now()
        )
        
        message_id = await db_manager.save_message(test_message)
        print(f"✅ Message saved with ID: {message_id}")
        
        messages = await db_manager.get_messages(limit=1)
        if messages:
            print("✅ Message retrieval successful")
        else:
            print("❌ No messages retrieved")
            return False
        
        await db_manager.close()
        
        # Clean up test database
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
        
        print("✅ Database test completed")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


async def test_mcp_server():
    """Test MCP server initialization."""
    print("\n🔍 Testing MCP server...")
    
    try:
        # Test that we can import and create the server
        from mcp.server.fastmcp import FastMCP
        
        mcp = FastMCP("test-server")
        print("✅ MCP server creation successful")
        
        # Test tool registration
        @mcp.tool()
        async def test_tool(message: str) -> str:
            """Test tool for verification."""
            return f"Echo: {message}"
        
        print("✅ MCP tool registration successful")
        return True
        
    except Exception as e:
        print(f"❌ MCP server test failed: {e}")
        return False


async def test_telegram_bot_config():
    """Test Telegram bot configuration."""
    print("\n🔍 Testing Telegram bot configuration...")
    
    try:
        with open("config/config.json", 'r') as f:
            config = json.load(f)
        
        bot_token = config["telegram"]["bot_token"]
        
        if bot_token == "YOUR_BOT_TOKEN_HERE":
            print("❌ Bot token not configured")
            print("Please run: python setup.py")
            return False
        
        if not bot_token.startswith(("1", "2", "5", "6", "7")):
            print("⚠️  Bot token format looks unusual")
        
        allowed_users = config["telegram"]["allowed_users"]
        if not allowed_users or allowed_users == [123456789]:
            print("⚠️  Default user IDs detected, please configure real user IDs")
        
        print("✅ Telegram configuration looks valid")
        return True
        
    except Exception as e:
        print(f"❌ Telegram configuration test failed: {e}")
        return False


async def test_file_structure():
    """Test that all required files exist."""
    print("\n🔍 Testing file structure...")
    
    required_files = [
        "main.py",
        "mcp_server.py",
        "telegram_bot.py",
        "message_bridge.py",
        "database.py",
        "requirements.txt",
        "config/config.json"
    ]
    
    required_dirs = [
        "data",
        "config"
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    for dir_path in required_dirs:
        if not os.path.isdir(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"  ✅ {dir_path}/")
    
    if missing_files or missing_dirs:
        if missing_files:
            print(f"❌ Missing files: {', '.join(missing_files)}")
        if missing_dirs:
            print(f"❌ Missing directories: {', '.join(missing_dirs)}")
        return False
    
    print("✅ File structure is complete")
    return True


async def main():
    """Run all tests."""
    print("🧪 Telegram MCP Server Installation Test")
    print("=" * 45)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("MCP Server", test_mcp_server),
        ("Telegram Config", test_telegram_bot_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your installation looks good.")
        print("\nYou can now:")
        print("1. Start the server: python main.py")
        print("2. Configure Claude Desktop with the MCP server")
        print("3. Test by messaging your Telegram bot")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("You may need to run: python setup.py")
    
    return passed == total


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
